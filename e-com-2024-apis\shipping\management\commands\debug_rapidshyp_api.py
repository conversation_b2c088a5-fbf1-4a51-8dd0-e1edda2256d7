"""
Django management command to debug Rapidshyp API calls
"""

import json
import requests
from datetime import datetime
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Debug Rapidshyp API calls to identify issues'

    def add_arguments(self, parser):
        parser.add_argument(
            '--test-serviceability',
            action='store_true',
            help='Test serviceability check API',
        )
        parser.add_argument(
            '--test-order-creation',
            action='store_true',
            help='Test order creation API with sample data',
        )
        parser.add_argument(
            '--pickup-pincode',
            type=str,
            default='110001',
            help='Pickup pincode for testing',
        )
        parser.add_argument(
            '--delivery-pincode',
            type=str,
            default='400001',
            help='Delivery pincode for testing',
        )

    def handle(self, *args, **options):
        # Check configuration
        api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')
        
        if not api_key:
            raise CommandError('RAPIDSHYP_API_KEY not configured')

        self.stdout.write(f'Using API Key: {api_key[:8]}***')
        self.stdout.write(f'Using Base URL: {base_url}')

        # Setup session
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': api_key,
            'User-Agent': 'Triumph-Ecommerce-Debug/1.0'
        })

        if options['test_serviceability']:
            self.test_serviceability_api(session, base_url, options)

        if options['test_order_creation']:
            self.test_order_creation_api(session, base_url, options)

    def test_serviceability_api(self, session, base_url, options):
        self.stdout.write('\n=== Testing Serviceability API ===')
        
        url = f"{base_url}/serviceabilty_check"
        payload = {
            "Pickup_pincode": options['pickup_pincode'],
            "Delivery_pincode": options['delivery_pincode'],
            "cod": False,
            "total_order_value": 1000.0,
            "weight": 1.0
        }

        self.stdout.write(f'URL: {url}')
        self.stdout.write(f'Payload: {json.dumps(payload, indent=2)}')

        try:
            response = session.post(url, json=payload, timeout=30)
            
            self.stdout.write(f'Status Code: {response.status_code}')
            self.stdout.write(f'Headers: {dict(response.headers)}')
            self.stdout.write(f'Content Length: {len(response.content)}')
            
            if response.content:
                self.stdout.write(f'Raw Content: {response.text[:1000]}')
                
                try:
                    json_data = response.json()
                    self.stdout.write(f'JSON Response: {json.dumps(json_data, indent=2)}')
                except json.JSONDecodeError as e:
                    self.stdout.write(self.style.ERROR(f'JSON Decode Error: {e}'))
            else:
                self.stdout.write(self.style.ERROR('Empty response content'))

        except requests.exceptions.RequestException as e:
            self.stdout.write(self.style.ERROR(f'Request Exception: {e}'))

    def test_order_creation_api(self, session, base_url, options):
        self.stdout.write('\n=== Testing Order Creation API ===')
        
        # Get configuration
        try:
            config = RapidshypConfiguration.objects.filter(is_active=True).first()
            if not config:
                self.stdout.write(self.style.ERROR('No Rapidshyp configuration found'))
                return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Configuration error: {e}'))
            return

        url = f"{base_url}/wrapper"
        
        # Sample order data for wrapper API
        order_data = {
            "orderId": "TEST-ORDER-123",
            "orderDate": datetime.now().strftime('%Y-%m-%d'),
            "pickupAddressName": config.pickup_address_name,
            "pickupLocation": {
                "contactName": config.contact_name,
                "pickupName": config.pickup_address_name,
                "pickupEmail": config.contact_email,
                "pickupPhone": config.contact_phone,
                "pickupAddress1": config.address_line_1,
                "pickupAddress2": config.address_line_2,
                "pinCode": config.default_pickup_pincode
            },
            "storeName": config.store_name,
            "billingIsShipping": True,
            "shippingAddress": {
                "firstName": "Test",
                "lastName": "Customer",
                "addressLine1": "123 Test Street",
                "addressLine2": "Test Area",
                "pinCode": options['delivery_pincode'],
                "email": "<EMAIL>",
                "phone": "9876543210"
            },
            "orderItems": [
                {
                    "itemName": "Test Product",
                    "sku": "TEST-SKU-001",
                    "description": "Test Product Description",
                    "units": 1,
                    "unitPrice": 1000.0,
                    "tax": 0.0,
                    "hsn": "",
                    "productLength": 10.0,
                    "productBreadth": 10.0,
                    "productHeight": 5.0,
                    "productWeight": 500,
                    "brand": "",
                    "imageURL": "",
                    "isFragile": False,
                    "isPersonalisable": False
                }
            ],
            "paymentMethod": "PREPAID",
            "shippingCharges": 0.0,
            "totalOrderValue": 1000.0,
            "packageDetails": {
                "packageLength": 10.0,
                "packageBreadth": 10.0,
                "packageHeight": 10.0,
                "packageWeight": 11000  # 11kg to exceed Shree Maruti's 10kg limit
            }
        }

        self.stdout.write(f'URL: {url}')
        self.stdout.write(f'Payload: {json.dumps(order_data, indent=2)}')

        try:
            response = session.post(url, json=order_data, timeout=30)
            
            self.stdout.write(f'Status Code: {response.status_code}')
            self.stdout.write(f'Headers: {dict(response.headers)}')
            self.stdout.write(f'Content Length: {len(response.content)}')
            
            if response.content:
                self.stdout.write(f'Raw Content: {response.text[:1000]}')
                
                try:
                    json_data = response.json()
                    self.stdout.write(f'JSON Response: {json.dumps(json_data, indent=2)}')
                except json.JSONDecodeError as e:
                    self.stdout.write(self.style.ERROR(f'JSON Decode Error: {e}'))
            else:
                self.stdout.write(self.style.ERROR('Empty response content'))

        except requests.exceptions.RequestException as e:
            self.stdout.write(self.style.ERROR(f'Request Exception: {e}'))
