"""
Constants for Rapidshyp integration
"""

# Rapidshyp API endpoints
RAPIDSHYP_BASE_URL = 'https://api.rapidshyp.com/rapidshyp/apis/v1'

RAPIDSHYP_ENDPOINTS = {
    'SERVICEABILITY_CHECK': '/serviceabilty_check',  # Note: This is the actual API endpoint (with typo)
    'WRAPPER': '/wrapper',
    'CREATE_ORDER': '/create_order',
    'UPDATE_ORDER': '/order_update',
    'ASSIGN_AWB': '/assign_awb',
    'SCHEDULE_PICKUP': '/schedule_pickup',
    'DEALLOCATE_SHIPMENT': '/de_allocate_shipment',
    'CANCEL_ORDER': '/cancel_order',
    'GENERATE_LABEL': '/generate_label',
    'CREATE_PICKUP_LOCATION': '/create/pickup_location',
    'ACTION_NDR': '/ndr/action',
    'TRACK_ORDER': '/track_order',
}

# Rapidshyp status codes mapping
RAPIDSHYP_STATUS_MAPPING = {
    'SCB': 'Shipment Booked',
    'PSH': 'Pickup Scheduled',
    'OFP': 'Out for Pickup',
    'PUE': 'Pick up Exception',
    'PCN': 'Pickup Cancelled',
    'PUC': 'Pickup Completed',
    'SPD': 'Shipped/Dispatched',
    'INT': 'In Transit',
    'RAD': 'Reached at Destination',
    'DED': 'Delivery Delayed',
    'OFD': 'Out for Delivery',
    'DEL': 'Delivered',
    'UND': 'Undelivered',
    'RTO_REQ': 'RTO Requested',
    'RTO': 'RTO Confirmed',
    'RTO_INT': 'RTO In Transit',
    'RTO_RAD': 'RTO - Reached at Destination',
    'RTO_OFD': 'RTO Out for Delivery',
    'RTO_DEL': 'RTO Delivered',
    'RTO_UND': 'RTO Undelivered',
    'CAN': 'Shipment Cancelled',
    'ONH': 'Shipment On Hold',
    'LST': 'Shipment Lost',
    'DMG': 'Shipment Damaged',
    'MSR': 'Shipment Misrouted',
    'DPO': 'Shipment Disposed-Off',
}

# Order status mapping to Django Order model
RAPIDSHYP_TO_ORDER_STATUS = {
    'SCB': 'PENDING',
    'PSH': 'PROCESSING',
    'OFP': 'PROCESSING',
    'PUC': 'SHIPPED',
    'SPD': 'SHIPPED',
    'INT': 'SHIPPED',
    'RAD': 'SHIPPED',
    'OFD': 'SHIPPED',
    'DEL': 'DELIVERED',
    'UND': 'SHIPPED',
    'RTO': 'RETURNED',
    'RTO_DEL': 'RETURNED',
    'CAN': 'CANCELLED',
}

# Cache settings
CACHE_SETTINGS = {
    'SHIPPING_RATES_TIMEOUT': 300,  # 5 minutes
    'TRACKING_DATA_TIMEOUT': 60,   # 1 minute
    'SERVICEABILITY_TIMEOUT': 3600, # 1 hour
}

# API request settings
API_SETTINGS = {
    'TIMEOUT': 30,  # seconds
    'RETRY_ATTEMPTS': 3,
    'RETRY_DELAY': 1,  # seconds
    'MAX_RETRY_DELAY': 10,  # seconds
}

# Validation settings
VALIDATION_SETTINGS = {
    'PINCODE_PATTERN': r'^\d{6}$',
    'PHONE_PATTERN': r'^[6-9]\d{9}$',
    'MIN_WEIGHT': 0.1,  # kg
    'MAX_WEIGHT': 50.0,  # kg
    'MIN_ORDER_VALUE': 1.0,  # rupees
}

# Default pickup location (can be overridden in settings)
DEFAULT_PICKUP_LOCATION = {
    'contactName': 'Triumph Enterprises',
    'pickupName': 'Main Warehouse',
    'pickupEmail': '<EMAIL>',
    'pickupPhone': '9848486452',
    'pickupAddress1': 'D.No. 5-5-190/65A, Ehata Nooruddin Shah Qadri',
    'pickupAddress2': 'Patel Nagar, Darussalam',
    'pinCode': '500001'
}

# Error messages
ERROR_MESSAGES = {
    'API_UNAVAILABLE': 'Rapidshyp service is temporarily unavailable. Using standard shipping rates.',
    'INVALID_PINCODE': 'Please enter a valid 6-digit pincode.',
    'PINCODE_NOT_SERVICEABLE': 'Delivery not available to this pincode.',
    'INVALID_WEIGHT': 'Package weight must be between {min_weight}kg and {max_weight}kg.',
    'AUTHENTICATION_FAILED': 'Rapidshyp authentication failed. Please check API credentials.',
    'RATE_LIMIT_EXCEEDED': 'Too many requests. Please try again later.',
    'ORDER_CREATION_FAILED': 'Failed to create Rapidshyp order. Order will use standard shipping.',
    'TRACKING_UNAVAILABLE': 'Tracking information is temporarily unavailable.',
}

# Success messages
SUCCESS_MESSAGES = {
    'RATES_CALCULATED': 'Shipping rates calculated successfully.',
    'ORDER_CREATED': 'Rapidshyp order created successfully.',
    'TRACKING_UPDATED': 'Tracking information updated successfully.',
    'PICKUP_SCHEDULED': 'Pickup scheduled successfully.',
}

# Problematic couriers (temporarily excluded due to API issues)
PROBLEMATIC_COURIERS = [
    '14001',  # Shree Maruti Surface - failing on Rapidshyp's end as of 2025-06-21
]

# Feature flags
FEATURE_FLAGS = {
    'ENABLE_RATE_CACHING': True,
    'ENABLE_TRACKING_SYNC': True,
    'ENABLE_AUTO_PICKUP_SCHEDULING': True,
    'ENABLE_LABEL_GENERATION': True,
    'ENABLE_WEBHOOK_PROCESSING': True,
    'FILTER_PROBLEMATIC_COURIERS': True,  # Filter out known problematic couriers
}

# Webhook settings
WEBHOOK_SETTINGS = {
    'ALLOWED_IPS': [
        # Add Rapidshyp webhook IPs here
    ],
    'SIGNATURE_HEADER': 'X-Rapidshyp-Signature',
    'TIMESTAMP_HEADER': 'X-Rapidshyp-Timestamp',
    'MAX_TIMESTAMP_DIFF': 300,  # 5 minutes
}

# Logging settings
LOGGING_SETTINGS = {
    'LOG_API_REQUESTS': True,
    'LOG_API_RESPONSES': True,
    'LOG_ERRORS_ONLY': False,
    'MAX_LOG_RETENTION_DAYS': 30,
}
