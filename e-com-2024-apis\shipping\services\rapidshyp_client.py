"""
Rapidshyp API client for shipping integration
"""

import requests
import json
import time
import logging
from typing import Dict, List, Optional, Union
from decimal import Decimal
from datetime import datetime, timedelta
from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

from ..constants import (
    RAPIDSHYP_ENDPOINTS,
    RAPIDSHYP_STATUS_MAPPING,
    API_SETTINGS,
    ERROR_MESSAGES
)
from ..exceptions import (
    Rapidshyp<PERSON>IException,
    RapidshypAuthenticationException,
    RapidshypServiceabilityException,
    RapidshypRateLimitException,
    RapidshypTimeoutException
)
from ..utils import (
    log_api_call,
    sanitize_for_api,
    validate_pincode,
    validate_weight,
    generate_cache_key
)


logger = logging.getLogger(__name__)


class RapidshypClient:
    """
    Rapidshyp API client with comprehensive error handling and fallback mechanisms
    """

    def __init__(self):
        self.api_key = getattr(settings, 'RAPIDSHYP_API_KEY', '')
        self.base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')
        self.timeout = API_SETTINGS['TIMEOUT']
        self.retry_attempts = API_SETTINGS['RETRY_ATTEMPTS']
        self.retry_delay = API_SETTINGS['RETRY_DELAY']
        self.max_retry_delay = API_SETTINGS['MAX_RETRY_DELAY']

        # Validate configuration
        if not self.api_key:
            logger.warning("Rapidshyp API key not configured")

        # Setup session with default headers
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': self.api_key,
            'User-Agent': 'Triumph-Ecommerce/1.0'
        })

    def _make_request(self, method: str, endpoint: str, data: Dict = None,
                     params: Dict = None) -> Dict:
        """
        Make HTTP request to Rapidshyp API with retry logic and error handling

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request payload
            params: URL parameters

        Returns:
            Dict: API response data

        Raises:
            RapidshypAPIException: For API errors
        """
        url = f"{self.base_url}{endpoint}"
        request_data = data or {}

        # Sanitize data for API call
        if data:
            request_data = sanitize_for_api(data)

        start_time = time.time()
        last_exception = None

        for attempt in range(self.retry_attempts):
            try:
                logger.debug(f"Rapidshyp API call attempt {attempt + 1}: {method} {url}")

                if method.upper() == 'GET':
                    response = self.session.get(url, params=params, timeout=self.timeout)
                elif method.upper() == 'POST':
                    response = self.session.post(url, json=request_data, params=params,
                                               timeout=self.timeout)
                else:
                    raise RapidshypAPIException(f"Unsupported HTTP method: {method}")

                response_time = int((time.time() - start_time) * 1000)

                # Log the API call with better error handling
                response_data = {}
                if response.content:
                    try:
                        response_data = response.json()
                    except json.JSONDecodeError:
                        response_data = {'raw_content': response.text[:500]}  # Log first 500 chars

                log_api_call(
                    method=endpoint.replace('/', '_').strip('_'),
                    endpoint=url,
                    request_data=request_data,
                    response_data=response_data,
                    response_time=response_time,
                    is_success=response.status_code == 200,
                    error_message='' if response.status_code == 200 else f"HTTP {response.status_code}"
                )

                # Handle different response status codes
                if response.status_code == 200:
                    if not response.content:
                        raise RapidshypAPIException("Empty response from Rapidshyp API")

                    try:
                        response_data = response.json()
                        logger.debug(f"Rapidshyp API success: {endpoint}")
                        return response_data
                    except json.JSONDecodeError as e:
                        logger.error(f"Invalid JSON response from {endpoint}. Content: {response.text[:500]}")
                        raise RapidshypAPIException(f"Invalid JSON response: {e}. Content: {response.text[:200]}")

                elif response.status_code == 401:
                    raise RapidshypAuthenticationException(
                        "Authentication failed. Please check API credentials.",
                        status_code=response.status_code
                    )

                elif response.status_code == 429:
                    raise RapidshypRateLimitException(
                        "Rate limit exceeded. Please try again later.",
                        status_code=response.status_code
                    )

                elif response.status_code >= 500:
                    # Server error - retry
                    error_msg = f"Server error: HTTP {response.status_code}"
                    logger.warning(f"Rapidshyp server error (attempt {attempt + 1}): {error_msg}")
                    last_exception = RapidshypAPIException(error_msg, status_code=response.status_code)

                else:
                    # Client error - don't retry
                    try:
                        error_data = response.json()
                        error_msg = error_data.get('message', error_data.get('remarks', f"HTTP {response.status_code}"))
                        # Include full error details for debugging
                        logger.error(f"Rapidshyp API error {response.status_code}: {error_data}")
                    except:
                        error_msg = f"HTTP {response.status_code}. Response: {response.text[:200]}"
                        logger.error(f"Rapidshyp API error {response.status_code}: {response.text}")

                    raise RapidshypAPIException(error_msg, status_code=response.status_code)

            except requests.exceptions.Timeout:
                error_msg = f"Request timeout after {self.timeout} seconds"
                logger.warning(f"Rapidshyp timeout (attempt {attempt + 1}): {error_msg}")
                last_exception = RapidshypTimeoutException(error_msg)

            except requests.exceptions.ConnectionError as e:
                error_msg = f"Connection error: {str(e)}"
                logger.warning(f"Rapidshyp connection error (attempt {attempt + 1}): {error_msg}")
                last_exception = RapidshypAPIException(error_msg)

            except requests.exceptions.RequestException as e:
                error_msg = f"Request error: {str(e)}"
                logger.error(f"Rapidshyp request error (attempt {attempt + 1}): {error_msg}")
                last_exception = RapidshypAPIException(error_msg)

            # Wait before retry (exponential backoff)
            if attempt < self.retry_attempts - 1:
                delay = min(self.retry_delay * (2 ** attempt), self.max_retry_delay)
                logger.debug(f"Retrying in {delay} seconds...")
                time.sleep(delay)

        # All retries failed
        if last_exception:
            raise last_exception
        else:
            raise RapidshypAPIException("All retry attempts failed")

    def check_serviceability(self, pickup_pincode: str, delivery_pincode: str,
                           cod: bool = False, total_order_value: float = 0,
                           weight: float = 1.0) -> Dict:
        """
        Check pincode serviceability and get available shipping rates

        Args:
            pickup_pincode: Pickup pincode
            delivery_pincode: Delivery pincode
            cod: Cash on delivery flag
            total_order_value: Total order value
            weight: Package weight in kg

        Returns:
            Dict: Serviceability response with available couriers and rates

        Raises:
            RapidshypServiceabilityException: If pincode is not serviceable
            RapidshypAPIException: For other API errors
        """
        # Validate inputs
        if not validate_pincode(pickup_pincode):
            raise RapidshypServiceabilityException(f"Invalid pickup pincode: {pickup_pincode}")

        if not validate_pincode(delivery_pincode):
            raise RapidshypServiceabilityException(f"Invalid delivery pincode: {delivery_pincode}")

        if not validate_weight(weight):
            raise RapidshypServiceabilityException(f"Invalid weight: {weight}kg")

        # Check cache first
        cache_key = generate_cache_key(
            'rapidshyp_serviceability',
            pickup=pickup_pincode,
            delivery=delivery_pincode,
            weight=weight,
            cod=cod,
            value=total_order_value
        )

        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Using cached serviceability data for {pickup_pincode} -> {delivery_pincode}")
            return cached_result

        # Prepare API request
        payload = {
            "Pickup_pincode": pickup_pincode,
            "Delivery_pincode": delivery_pincode,
            "cod": cod,
            "total_order_value": float(total_order_value),
            "weight": float(weight)
        }

        try:
            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['SERVICEABILITY_CHECK'], payload)

            # Validate response structure
            if not isinstance(response, dict):
                raise RapidshypAPIException("Invalid response format")

            # Check if serviceability check was successful
            if not response.get('status', False):
                error_msg = response.get('message', 'Pincode not serviceable')
                raise RapidshypServiceabilityException(error_msg)

            # Validate courier list
            courier_list = response.get('serviceable_courier_list', [])
            if not courier_list:
                raise RapidshypServiceabilityException("No couriers available for this route")

            # Cache successful result
            cache_timeout = getattr(settings, 'RAPIDSHYP_CACHE_SETTINGS', {}).get('SHIPPING_RATES_TIMEOUT', 300)
            cache.set(cache_key, response, cache_timeout)

            logger.info(f"Serviceability check successful: {pickup_pincode} -> {delivery_pincode}, "
                       f"{len(courier_list)} couriers available")

            return response

        except RapidshypServiceabilityException:
            # Re-raise serviceability exceptions
            raise
        except Exception as e:
            logger.error(f"Serviceability check failed: {e}")
            raise RapidshypAPIException(f"Serviceability check failed: {e}")

    def create_order(self, order_data: Dict) -> Dict:
        """
        Create order using Rapidshyp wrapper API

        Args:
            order_data: Complete order data for Rapidshyp

        Returns:
            Dict: Order creation response

        Raises:
            RapidshypAPIException: For API errors
        """
        try:
            # Validate required fields for wrapper API
            required_fields = ['orderId', 'orderDate', 'pickupLocation', 'shippingAddress', 'orderItems', 'packageDetails']
            for field in required_fields:
                if field not in order_data:
                    raise RapidshypAPIException(f"Missing required field: {field}")

            # Log the order data being sent for debugging
            logger.info(f"Sending order data to Rapidshyp: {order_data}")

            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['WRAPPER'], order_data)

            # Validate response
            if not isinstance(response, dict):
                raise RapidshypAPIException("Invalid response format")

            # Check if order creation was successful
            if response.get('status') == 'FAILED' or not response.get('orderCreated', False):
                error_msg = response.get('remarks', response.get('message', 'Order creation failed'))
                logger.error(f"Rapidshyp order creation failed: {error_msg}. Full response: {response}")
                raise RapidshypAPIException(error_msg)

            logger.info(f"Order created successfully: {response.get('orderId', 'Unknown')}")
            return response

        except RapidshypAPIException:
            # Re-raise API exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Order creation failed: {e}")
            raise RapidshypAPIException(f"Order creation failed: {e}")

    def track_shipment(self, awb: str = None, order_id: str = None) -> Dict:
        """
        Track shipment by AWB number or order ID

        Args:
            awb: AWB number
            order_id: Rapidshyp order ID

        Returns:
            Dict: Tracking information

        Raises:
            RapidshypAPIException: For API errors
        """
        if not awb and not order_id:
            raise RapidshypAPIException("Either AWB number or order ID is required for tracking")

        # Check cache first
        cache_key = generate_cache_key(
            'rapidshyp_tracking',
            awb=awb or '',
            order_id=order_id or ''
        )

        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Using cached tracking data for {awb or order_id}")
            return cached_result

        payload = {}
        if awb:
            payload['awb'] = awb
        if order_id:
            payload['seller_order_id'] = order_id

        try:
            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['TRACK_ORDER'], payload)

            # Validate response
            if not isinstance(response, dict):
                raise RapidshypAPIException("Invalid tracking response format")

            # Cache result for shorter time (tracking data changes frequently)
            cache_timeout = getattr(settings, 'RAPIDSHYP_CACHE_SETTINGS', {}).get('TRACKING_DATA_TIMEOUT', 60)
            cache.set(cache_key, response, cache_timeout)

            logger.debug(f"Tracking data retrieved for {awb or order_id}")
            return response

        except Exception as e:
            logger.error(f"Tracking failed: {e}")
            raise RapidshypAPIException(f"Tracking failed: {e}")

    def create_order_direct(self, order_data: Dict) -> Dict:
        """
        Create order using Rapidshyp Create Order API (not wrapper)

        Args:
            order_data: Complete order data for Rapidshyp

        Returns:
            Dict: Order creation response

        Raises:
            RapidshypAPIException: For API errors
        """
        try:
            # Validate required fields for create order API
            # Note: pickupLocation is optional if pickupAddressName is provided
            required_fields = ['orderId', 'orderDate', 'shippingAddress', 'orderItems', 'packageDetails']
            for field in required_fields:
                if field not in order_data:
                    raise RapidshypAPIException(f"Missing required field: {field}")

            # Validate pickup configuration - either pickupAddressName or pickupLocation must be present
            if 'pickupAddressName' not in order_data and 'pickupLocation' not in order_data:
                raise RapidshypAPIException("Either pickupAddressName or pickupLocation must be provided")

            # Log the order data being sent for debugging
            logger.info(f"Sending order data to Rapidshyp Create Order API: {order_data}")

            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['CREATE_ORDER'], order_data)

            # Validate response
            if not isinstance(response, dict):
                raise RapidshypAPIException("Invalid response format")

            # Check if order creation was successful
            if response.get('status') == 'FAILED' or not response.get('orderCreated', False):
                error_msg = response.get('remarks', response.get('message', 'Order creation failed'))
                logger.error(f"Rapidshyp order creation failed: {error_msg}. Full response: {response}")
                raise RapidshypAPIException(error_msg)

            logger.info(f"Order created successfully: {response.get('orderId', 'Unknown')}")
            return response

        except RapidshypAPIException:
            # Re-raise API exceptions as-is
            raise
        except Exception as e:
            logger.error(f"Order creation failed: {e}")
            raise RapidshypAPIException(f"Order creation failed: {e}")

    def assign_awb(self, shipment_id: str, courier_code: str = "") -> Dict:
        """
        Assign AWB number to a shipment

        Args:
            shipment_id: Rapidshyp shipment ID
            courier_code: Selected courier code (optional)

        Returns:
            Dict: AWB assignment response
        """
        payload = {
            "shipment_id": shipment_id,
            "courier_code": courier_code
        }

        try:
            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['ASSIGN_AWB'], payload)

            if not response.get('status', False):
                error_msg = response.get('message', 'AWB assignment failed')
                raise RapidshypAPIException(error_msg)

            logger.info(f"AWB assigned successfully for shipment {shipment_id}")
            return response

        except Exception as e:
            logger.error(f"AWB assignment failed: {e}")
            raise RapidshypAPIException(f"AWB assignment failed: {e}")

    def schedule_pickup(self, order_id: str, pickup_date: str = None) -> Dict:
        """
        Schedule pickup for an order

        Args:
            order_id: Rapidshyp order ID
            pickup_date: Pickup date (YYYY-MM-DD format)

        Returns:
            Dict: Pickup scheduling response
        """
        payload = {"order_id": order_id}

        if pickup_date:
            payload["pickup_date"] = pickup_date

        try:
            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['SCHEDULE_PICKUP'], payload)

            if not response.get('status', False):
                error_msg = response.get('message', 'Pickup scheduling failed')
                raise RapidshypAPIException(error_msg)

            logger.info(f"Pickup scheduled successfully for order {order_id}")
            return response

        except Exception as e:
            logger.error(f"Pickup scheduling failed: {e}")
            raise RapidshypAPIException(f"Pickup scheduling failed: {e}")

    def cancel_order(self, order_id: str, reason: str = "Customer request") -> Dict:
        """
        Cancel an order

        Args:
            order_id: Rapidshyp order ID
            reason: Cancellation reason

        Returns:
            Dict: Cancellation response
        """
        payload = {
            "order_id": order_id,
            "reason": reason
        }

        try:
            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['CANCEL_ORDER'], payload)

            if not response.get('status', False):
                error_msg = response.get('message', 'Order cancellation failed')
                raise RapidshypAPIException(error_msg)

            logger.info(f"Order cancelled successfully: {order_id}")
            return response

        except Exception as e:
            logger.error(f"Order cancellation failed: {e}")
            raise RapidshypAPIException(f"Order cancellation failed: {e}")

    def generate_label(self, order_id: str) -> Dict:
        """
        Generate shipping label for an order

        Args:
            order_id: Rapidshyp order ID

        Returns:
            Dict: Label generation response with URL
        """
        payload = {"order_id": order_id}

        try:
            response = self._make_request('POST', RAPIDSHYP_ENDPOINTS['GENERATE_LABEL'], payload)

            if not response.get('status', False):
                error_msg = response.get('message', 'Label generation failed')
                raise RapidshypAPIException(error_msg)

            logger.info(f"Label generated successfully for order {order_id}")
            return response

        except Exception as e:
            logger.error(f"Label generation failed: {e}")
            raise RapidshypAPIException(f"Label generation failed: {e}")

    def get_status_description(self, status_code: str) -> str:
        """
        Get human-readable description for status code

        Args:
            status_code: Rapidshyp status code

        Returns:
            str: Human-readable status description
        """
        return RAPIDSHYP_STATUS_MAPPING.get(status_code, status_code)

    def is_api_available(self) -> bool:
        """
        Check if Rapidshyp API is available

        Returns:
            bool: True if API is available, False otherwise
        """
        if not self.api_key:
            return False

        try:
            # Simple serviceability check to test API availability
            test_response = self.check_serviceability(
                pickup_pincode="110001",
                delivery_pincode="400001",
                weight=1.0
            )
            return True
        except Exception as e:
            logger.warning(f"Rapidshyp API availability check failed: {e}")
            return False
